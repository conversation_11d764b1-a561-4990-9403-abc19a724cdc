import uuid
from typing import Any
from psycopg import Connection

from app.core.security import get_password_hash, verify_password
from app.models import ItemCreate, ItemPublic, UserCreate, UserPublic, UserUpdate


def create_user(*, conn: Connection, user_create: UserCreate) -> UserPublic:
    user_id = uuid.uuid4()
    hashed_password = get_password_hash(user_create.password)

    with conn.cursor() as cur:
        cur.execute(
            """
            INSERT INTO users (id, email, hashed_password, is_active, is_superuser, full_name)
            VALUES (%s, %s, %s, %s, %s, %s)
            RETURNING id, email, is_active, is_superuser, full_name
            """,
            (user_id, user_create.email, hashed_password, user_create.is_active,
             user_create.is_superuser, user_create.full_name)
        )
        user_data = cur.fetchone()
        conn.commit()

    return UserPublic(
        id=user_data["id"],
        email=user_data["email"],
        is_active=user_data["is_active"],
        is_superuser=user_data["is_superuser"],
        full_name=user_data["full_name"]
    )


def update_user(*, conn: Connection, user_id: uuid.UUID, user_in: UserUpdate) -> UserPublic:
    user_data = user_in.model_dump(exclude_unset=True)

    # Build dynamic update query
    set_clauses = []
    values = []

    for field, value in user_data.items():
        if field == "password":
            set_clauses.append("hashed_password = %s")
            values.append(get_password_hash(value))
        else:
            set_clauses.append(f"{field} = %s")
            values.append(value)

    if not set_clauses:
        # No fields to update, just return current user
        return get_user_by_id(conn=conn, user_id=user_id)

    values.append(user_id)

    with conn.cursor() as cur:
        cur.execute(
            f"""
            UPDATE users
            SET {', '.join(set_clauses)}
            WHERE id = %s
            RETURNING id, email, is_active, is_superuser, full_name
            """,
            values
        )
        user_data = cur.fetchone()
        conn.commit()

    return UserPublic(
        id=user_data["id"],
        email=user_data["email"],
        is_active=user_data["is_active"],
        is_superuser=user_data["is_superuser"],
        full_name=user_data["full_name"]
    )


def get_user_by_email(*, conn: Connection, email: str) -> UserPublic | None:
    with conn.cursor() as cur:
        cur.execute(
            "SELECT id, email, hashed_password, is_active, is_superuser, full_name FROM users WHERE email = %s",
            (email,)
        )
        user_data = cur.fetchone()

    if not user_data:
        return None

    return UserPublic(
        id=user_data["id"],
        email=user_data["email"],
        is_active=user_data["is_active"],
        is_superuser=user_data["is_superuser"],
        full_name=user_data["full_name"]
    )


def get_user_by_id(*, conn: Connection, user_id: uuid.UUID) -> UserPublic | None:
    with conn.cursor() as cur:
        cur.execute(
            "SELECT id, email, is_active, is_superuser, full_name FROM users WHERE id = %s",
            (user_id,)
        )
        user_data = cur.fetchone()

    if not user_data:
        return None

    return UserPublic(
        id=user_data["id"],
        email=user_data["email"],
        is_active=user_data["is_active"],
        is_superuser=user_data["is_superuser"],
        full_name=user_data["full_name"]
    )


def authenticate(*, conn: Connection, email: str, password: str) -> UserPublic | None:
    with conn.cursor() as cur:
        cur.execute(
            "SELECT id, email, hashed_password, is_active, is_superuser, full_name FROM users WHERE email = %s",
            (email,)
        )
        user_data = cur.fetchone()

    if not user_data:
        return None

    if not verify_password(password, user_data["hashed_password"]):
        return None

    return UserPublic(
        id=user_data["id"],
        email=user_data["email"],
        is_active=user_data["is_active"],
        is_superuser=user_data["is_superuser"],
        full_name=user_data["full_name"]
    )


def create_item(*, conn: Connection, item_in: ItemCreate, owner_id: uuid.UUID) -> ItemPublic:
    item_id = uuid.uuid4()

    with conn.cursor() as cur:
        cur.execute(
            """
            INSERT INTO items (id, title, description, owner_id)
            VALUES (%s, %s, %s, %s)
            RETURNING id, title, description, owner_id
            """,
            (item_id, item_in.title, item_in.description, owner_id)
        )
        item_data = cur.fetchone()
        conn.commit()

    return ItemPublic(
        id=item_data["id"],
        title=item_data["title"],
        description=item_data["description"],
        owner_id=item_data["owner_id"]
    )


def get_item_by_id(*, conn: Connection, item_id: uuid.UUID) -> ItemPublic | None:
    with conn.cursor() as cur:
        cur.execute(
            "SELECT id, title, description, owner_id FROM items WHERE id = %s",
            (item_id,)
        )
        item_data = cur.fetchone()

    if not item_data:
        return None

    return ItemPublic(
        id=item_data["id"],
        title=item_data["title"],
        description=item_data["description"],
        owner_id=item_data["owner_id"]
    )


def get_items(*, conn: Connection, skip: int = 0, limit: int = 100, owner_id: uuid.UUID | None = None) -> tuple[list[ItemPublic], int]:
    with conn.cursor() as cur:
        # Count query
        if owner_id:
            cur.execute("SELECT COUNT(*) FROM items WHERE owner_id = %s", (owner_id,))
        else:
            cur.execute("SELECT COUNT(*) FROM items")
        count = cur.fetchone()[0]

        # Data query
        if owner_id:
            cur.execute(
                "SELECT id, title, description, owner_id FROM items WHERE owner_id = %s ORDER BY title OFFSET %s LIMIT %s",
                (owner_id, skip, limit)
            )
        else:
            cur.execute(
                "SELECT id, title, description, owner_id FROM items ORDER BY title OFFSET %s LIMIT %s",
                (skip, limit)
            )
        items_data = cur.fetchall()

    items = [
        ItemPublic(
            id=item["id"],
            title=item["title"],
            description=item["description"],
            owner_id=item["owner_id"]
        )
        for item in items_data
    ]

    return items, count


def update_item(*, conn: Connection, item_id: uuid.UUID, item_update: dict) -> ItemPublic | None:
    # Build dynamic update query
    set_clauses = []
    values = []

    for field, value in item_update.items():
        if value is not None:
            set_clauses.append(f"{field} = %s")
            values.append(value)

    if not set_clauses:
        # No fields to update, just return current item
        return get_item_by_id(conn=conn, item_id=item_id)

    values.append(item_id)

    with conn.cursor() as cur:
        cur.execute(
            f"""
            UPDATE items
            SET {', '.join(set_clauses)}
            WHERE id = %s
            RETURNING id, title, description, owner_id
            """,
            values
        )
        item_data = cur.fetchone()
        conn.commit()

    if not item_data:
        return None

    return ItemPublic(
        id=item_data["id"],
        title=item_data["title"],
        description=item_data["description"],
        owner_id=item_data["owner_id"]
    )


def delete_item(*, conn: Connection, item_id: uuid.UUID) -> bool:
    with conn.cursor() as cur:
        cur.execute("DELETE FROM items WHERE id = %s", (item_id,))
        conn.commit()
        return cur.rowcount > 0


def get_users(*, conn: Connection, skip: int = 0, limit: int = 100) -> tuple[list[UserPublic], int]:
    with conn.cursor() as cur:
        # Count query
        cur.execute("SELECT COUNT(*) FROM users")
        count = cur.fetchone()[0]

        # Data query
        cur.execute(
            "SELECT id, email, is_active, is_superuser, full_name FROM users ORDER BY email OFFSET %s LIMIT %s",
            (skip, limit)
        )
        users_data = cur.fetchall()

    users = [
        UserPublic(
            id=user["id"],
            email=user["email"],
            is_active=user["is_active"],
            is_superuser=user["is_superuser"],
            full_name=user["full_name"]
        )
        for user in users_data
    ]

    return users, count


def delete_user(*, conn: Connection, user_id: uuid.UUID) -> bool:
    with conn.cursor() as cur:
        cur.execute("DELETE FROM users WHERE id = %s", (user_id,))
        conn.commit()
        return cur.rowcount > 0


def get_user_hashed_password(*, conn: Connection, user_id: uuid.UUID) -> str | None:
    """Get the hashed password for a user by ID."""
    with conn.cursor() as cur:
        cur.execute("SELECT hashed_password FROM users WHERE id = %s", (user_id,))
        result = cur.fetchone()
        return result["hashed_password"] if result else None
