import logging

import psycopg
from tenacity import after_log, before_log, retry, stop_after_attempt, wait_fixed

from app.core.db import get_database_url

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

max_tries = 60 * 5  # 5 minutes
wait_seconds = 1


@retry(
    stop=stop_after_attempt(max_tries),
    wait=wait_fixed(wait_seconds),
    before=before_log(logger, logging.INFO),
    after=after_log(logger, logging.WARN),
)
def init() -> None:
    try:
        with psycopg.connect(get_database_url()) as conn:
            with conn.cursor() as cur:
                # Try to execute a simple query to check if <PERSON> is awake
                cur.execute("SELECT 1")
    except Exception as e:
        logger.error(e)
        raise e


def main() -> None:
    logger.info("Initializing service")
    init()
    logger.info("Service finished initializing")


if __name__ == "__main__":
    main()
