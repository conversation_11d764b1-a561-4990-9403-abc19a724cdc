from collections.abc import Generator
from typing import Annotated
import uuid

import jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import OA<PERSON>2P<PERSON>wordBearer
from jwt.exceptions import InvalidTokenError
from pydantic import ValidationError
from psycopg import Connection

from app.core import security
from app.core.config import settings
from app.core.db import get_db_connection
from app.models import TokenPayload, UserPublic

reusable_oauth2 = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/login/access-token"
)


def get_db() -> Generator[Connection, None, None]:
    with get_db_connection() as conn:
        yield conn


ConnectionDep = Annotated[Connection, Depends(get_db)]
TokenDep = Annotated[str, Depends(reusable_oauth2)]


def get_current_user(conn: ConnectionDep, token: TokenDep) -> UserPublic:
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[security.ALGORITHM]
        )
        token_data = TokenPayload(**payload)
    except (InvalidTokenError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Could not validate credentials",
        )

    with conn.cursor() as cur:
        cur.execute(
            "SELECT id, email, is_active, is_superuser, full_name FROM users WHERE id = %s",
            (token_data.sub,)
        )
        user_data = cur.fetchone()

    if not user_data:
        raise HTTPException(status_code=404, detail="User not found")

    user = UserPublic(
        id=user_data["id"],
        email=user_data["email"],
        is_active=user_data["is_active"],
        is_superuser=user_data["is_superuser"],
        full_name=user_data["full_name"]
    )

    if not user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return user


CurrentUser = Annotated[UserPublic, Depends(get_current_user)]


def get_current_active_superuser(current_user: CurrentUser) -> UserPublic:
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403, detail="The user doesn't have enough privileges"
        )
    return current_user
