name: Issue Manager

on:
  schedule:
    - cron: "21 17 * * *"
  issue_comment:
    types:
      - created
  issues:
    types:
      - labeled
  pull_request_target:
    types:
      - labeled
  workflow_dispatch:

permissions:
  issues: write
  pull-requests: write

jobs:
  issue-manager:
    if: github.repository_owner == 'fastapi'
    runs-on: ubuntu-latest
    steps:
      - name: Dump GitHub context
        env:
          GITHUB_CONTEXT: ${{ toJson(github) }}
        run: echo "$GITHUB_CONTEXT"
      - uses: tiangolo/issue-manager@0.5.1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          config: >
            {
              "answered": {
                "delay": 864000,
                "message": "Assuming the original need was handled, this will be automatically closed now. But feel free to add more comments or create new issues or PRs."
              },
              "waiting": {
                "delay": 2628000,
                "message": "As this PR has been waiting for the original user for a while but seems to be inactive, it's now going to be closed. But if there's anyone interested, feel free to create a new PR."
              },
              "invalid": {
                "delay": 0,
                "message": "This was marked as invalid and will be closed now. If this is an error, please provide additional details."
              }
            }
