import psycopg
from psycopg import Connection
from psycopg.rows import dict_row
from contextlib import contextmanager
from typing import Generator
import uuid

from app.core.config import settings
from app.core.security import get_password_hash


def get_database_url() -> str:
    """Get the database URL for psycopg connection."""
    return f"postgresql://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_SERVER}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}"


@contextmanager
def get_db_connection() -> Generator[Connection, None, None]:
    """Get a database connection with automatic cleanup."""
    conn = None
    try:
        conn = psycopg.connect(get_database_url(), row_factory=dict_row)
        yield conn
    finally:
        if conn:
            conn.close()


def init_db() -> None:
    """Initialize the database with required tables and initial data."""
    from app.sql.init_db import main as init_db_main
    init_db_main()
