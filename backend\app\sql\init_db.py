#!/usr/bin/env python3
"""
Database initialization script.
This script creates the database schema and initial data.
"""

import logging
import os
from pathlib import Path

import psycopg

from app.core.config import settings
from app.core.db import get_database_url
from app.core.security import get_password_hash

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def read_sql_file(file_path: Path) -> str:
    """Read SQL file content."""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()


def create_schema(conn: psycopg.Connection) -> None:
    """Create database schema from SQL file."""
    sql_dir = Path(__file__).parent
    schema_file = sql_dir / "schema.sql"
    
    if not schema_file.exists():
        raise FileNotFoundError(f"Schema file not found: {schema_file}")
    
    schema_sql = read_sql_file(schema_file)
    
    with conn.cursor() as cur:
        logger.info("Creating database schema...")
        cur.execute(schema_sql)
        conn.commit()
        logger.info("Database schema created successfully")


def create_initial_superuser(conn: psycopg.Connection) -> None:
    """Create initial superuser if it doesn't exist."""
    with conn.cursor() as cur:
        # Check if superuser already exists
        cur.execute(
            "SELECT id FROM users WHERE email = %s",
            (settings.FIRST_SUPERUSER,)
        )
        user = cur.fetchone()
        
        if not user:
            logger.info(f"Creating initial superuser: {settings.FIRST_SUPERUSER}")
            hashed_password = get_password_hash(settings.FIRST_SUPERUSER_PASSWORD)
            cur.execute(
                """
                INSERT INTO users (email, hashed_password, is_active, is_superuser, full_name)
                VALUES (%s, %s, %s, %s, %s)
                """,
                (
                    settings.FIRST_SUPERUSER,
                    hashed_password,
                    True,
                    True,
                    "Super User"
                )
            )
            conn.commit()
            logger.info("Initial superuser created successfully")
        else:
            logger.info("Initial superuser already exists")


def main() -> None:
    """Main initialization function."""
    logger.info("Starting database initialization...")
    
    try:
        with psycopg.connect(get_database_url()) as conn:
            create_schema(conn)
            create_initial_superuser(conn)
        
        logger.info("Database initialization completed successfully")
    
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise


if __name__ == "__main__":
    main()
